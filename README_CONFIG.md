# 筛查配置文件支持功能

## 功能概述

本次更新为结直肠癌筛查微观仿真模型增加了对Excel和JSON格式配置文件的支持，允许用户通过外部配置文件定义筛查工具参数和筛查策略，实现模型初始化时的灵活配置。

## 主要特性

### ✅ 支持的配置格式
- **JSON格式**: 筛查参数配置（敏感性、特异性、依从性、成本等）
- **Excel格式**: 筛查策略配置（策略定义和工具配置）

### ✅ 核心功能
1. **模型初始化时自动加载配置**
2. **运行时动态加载配置**
3. **配置文件的保存和导出**
4. **配置模板文件生成**
5. **完整的错误处理和向后兼容**

## 文件结构

```
config/
├── screening_parameters.json      # 筛查参数配置
├── screening_strategies.xlsx      # 筛查策略配置
├── example_screening_parameters.json
└── example_screening_strategies.xlsx
```

## 配置文件格式

### 1. 筛查参数配置 (JSON)

```json
{
  "sensitivity": {
    "FIT": {
      "LOW_RISK_ADENOMA": 0.15,
      "HIGH_RISK_ADENOMA": 0.25,
      "PRECLINICAL_CANCER": 0.70,
      ...
    },
    "COLONOSCOPY": { ... },
    "SIGMOIDOSCOPY": { ... },
    "RISK_QUESTIONNAIRE": { ... }
  },
  "specificity": {
    "FIT": 0.95,
    "COLONOSCOPY": 0.90,
    ...
  },
  "compliance": { ... },
  "follow_up_compliance": { ... },
  "costs": { ... }
}
```

### 2. 筛查策略配置 (Excel)

**strategies工作表:**
| strategy_name | sequential | risk_stratified | high_risk_interval |
|---------------|------------|-----------------|-------------------|
| custom_annual_fit | False | False | |
| custom_risk_stratified | True | True | 1.0 |

**tool_configs工作表:**
| strategy_name | tool | start_age | end_age | interval | compliance_rate | follow_up_compliance_rate |
|---------------|------|-----------|---------|----------|-----------------|---------------------------|
| custom_annual_fit | FIT | 50 | 75 | 1.0 | 0.70 | 0.75 |

## 使用方法

### 方法1: 模型初始化时加载

```python
from src.ccsm.core.model import ColorectalCancerMicrosimulationModel, ModelConfiguration

# 创建配置
config = ModelConfiguration(
    initial_population=10000,
    load_config_from_files=True,
    config_dir="config",
    screening_params_file="screening_parameters.json",
    screening_strategies_file="screening_strategies.xlsx"
)

# 初始化模型
model = ColorectalCancerMicrosimulationModel(config=config)
```

### 方法2: 运行时加载

```python
# 初始化模型
model = ColorectalCancerMicrosimulationModel(initial_population=10000)

# 加载配置
model.load_screening_config_from_files(
    params_file="config/screening_parameters.json",
    strategies_file="config/screening_strategies.xlsx"
)
```

### 方法3: 创建和保存配置

```python
# 创建配置模板
model.create_screening_config_templates()

# 保存当前配置
model.save_screening_config_to_files(
    params_file="config/my_screening_parameters.json",
    strategies_file="config/my_screening_strategies.xlsx"
)
```

## 支持的枚举值

### 筛查工具 (ScreeningTool)
- `FIT`: 粪便免疫化学检测
- `COLONOSCOPY`: 结肠镜检查
- `SIGMOIDOSCOPY`: 乙状结肠镜检查
- `RISK_QUESTIONNAIRE`: 风险问卷

### 癌症阶段 (CancerStage)
- `NORMAL`: 正常状态
- `LOW_RISK_ADENOMA`: 低风险腺瘤
- `HIGH_RISK_ADENOMA`: 高风险腺瘤
- `SMALL_SERRATED_ADENOMA`: 小锯齿状腺瘤
- `LARGE_SERRATED_ADENOMA`: 大锯齿状腺瘤
- `PRECLINICAL_CANCER`: 临床前癌症
- `CLINICAL_CANCER_STAGE_I`: 临床癌症I期
- `CLINICAL_CANCER_STAGE_II`: 临床癌症II期
- `CLINICAL_CANCER_STAGE_III`: 临床癌症III期
- `CLINICAL_CANCER_STAGE_IV`: 临床癌症IV期

## 示例运行

```bash
# 运行简单示例
python examples/simple_config_example.py

# 运行完整测试
python examples/test_config_loading.py
```

## 新增的核心模块

### 1. `src/ccsm/modules/config_loader.py`
- `ScreeningConfigLoader`: 配置文件加载器
- `ScreeningConfigManager`: 配置管理器

### 2. 主模型类增强
- 新增配置加载相关方法
- 支持配置文件路径参数
- 自动配置加载和错误处理

## 错误处理

- **文件不存在**: 自动使用默认配置并显示警告
- **格式错误**: 显示详细错误信息并回退到默认配置
- **枚举值错误**: 自动映射和转换常见的命名变体
- **向后兼容**: 完全兼容现有代码，不影响原有功能

## 技术实现

### 核心特性
- 使用pandas处理Excel文件
- JSON格式的参数序列化/反序列化
- 枚举类型的智能转换和映射
- 完整的数据验证和错误处理
- 模块化设计，易于扩展

### 性能优化
- 配置文件缓存机制
- 延迟加载策略
- 最小化内存占用

## 总结

通过这次更新，用户现在可以：

1. **灵活配置**: 通过外部文件定义筛查参数和策略
2. **易于管理**: 使用熟悉的Excel和JSON格式
3. **版本控制**: 配置文件可以独立进行版本管理
4. **团队协作**: 不同团队成员可以共享和修改配置
5. **批量实验**: 快速切换不同的配置进行对比实验

这大大提高了模型的可用性和灵活性，使得非编程用户也能够轻松配置和使用模型。
