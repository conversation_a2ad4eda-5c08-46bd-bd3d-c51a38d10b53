# 统一配置加载器 - 整合完成

## 概述

已成功将 `data_loader.py` 与 `config_loader.py` 整合为统一的配置加载器 `UnifiedConfigLoader`，并将所有数据文件路径统一到 `config` 文件夹，实现了模型所有数据输入的统一入口。

## 主要改进

### ✅ 文件整合
- **合并模块**: `data_loader.py` + `config_loader.py` → `config_loader.py`
- **统一类名**: `UnifiedConfigLoader` 和 `UnifiedConfigManager`
- **向后兼容**: 保留原类名作为别名

### ✅ 路径统一
- **统一目录**: 所有数据文件移动到 `config/` 目录
- **路径处理**: 支持相对路径和绝对路径
- **自动解析**: 相对路径自动相对于配置目录

### ✅ 功能整合
- **筛查配置**: JSON参数 + Excel策略
- **基准数据**: 校准目标数据
- **寿命表数据**: 死亡率数据
- **人口构成**: 年龄分布和性别比例
- **筛查性能**: 工具性能指标

## 文件结构

```
config/
├── screening_parameters.json      # 筛查参数配置
├── screening_strategies.xlsx      # 筛查策略配置
├── default_benchmarks.xlsx        # 基准数据
├── lifetable.xlsx                 # 寿命表数据
├── population.xlsx                # 人口构成数据
└── screening_data.xlsx            # 筛查性能数据（可选）
```

## 核心类

### UnifiedConfigLoader
```python
class UnifiedConfigLoader:
    """统一配置加载器"""
    
    # 筛查配置方法
    def load_screening_parameters_from_json()
    def load_screening_strategies_from_excel()
    def save_screening_parameters_to_json()
    def save_screening_strategies_to_excel()
    
    # 数据加载方法
    def load_benchmark_data()
    def load_lifetable_data()
    def load_population_data()
    def load_screening_data()
    def load_custom_lifetable()
    
    # 数据处理方法
    def _process_benchmark_data()
    def _process_lifetable_data()
    def _process_population_data()
    def _extract_screening_data()
```

### UnifiedConfigManager
```python
class UnifiedConfigManager:
    """统一配置管理器"""
    
    # 配置管理
    def initialize_from_config()
    def add_strategy()
    def remove_strategy()
    def save_current_config()
    
    # 数据加载便利方法
    def load_benchmark_data()
    def load_lifetable_data()
    def load_population_data()
    def load_screening_data()
    def load_all_data()
```

## 使用方法

### 1. 模型初始化时加载所有配置

```python
from src.ccsm.core.model import ColorectalCancerMicrosimulationModel, ModelConfiguration

config = ModelConfiguration(
    initial_population=10000,
    load_config_from_files=True,
    config_dir="config"
)

model = ColorectalCancerMicrosimulationModel(config=config)
```

### 2. 直接使用统一配置管理器

```python
from src.ccsm.modules.config_loader import UnifiedConfigManager

config_manager = UnifiedConfigManager("config")

# 加载所有数据
all_data = config_manager.load_all_data()

# 单独加载特定数据
benchmark_data = config_manager.load_benchmark_data()
lifetable_data = config_manager.load_lifetable_data()
population_data = config_manager.load_population_data()
```

### 3. 自定义生命表加载

```python
# 支持Excel和CSV格式
lifetable_data = config_manager.load_custom_lifetable("my_lifetable.xlsx")
```

## 测试验证

### 运行测试
```bash
# 基本功能测试
python examples/simple_config_example.py

# 完整功能测试
python examples/test_unified_config.py
```

### 测试结果
```
✅ 筛查参数和策略配置加载
✅ 基准数据加载 (5个指标组)
✅ 寿命表数据加载 (151个年龄组)
✅ 人口构成数据加载 (26个年龄组, 179万人口)
✅ 与主模型完全集成
✅ 所有数据文件统一到config目录
```

## 数据格式支持

### 1. 筛查参数 (JSON)
```json
{
  "sensitivity": {
    "FIT": {"LOW_RISK_ADENOMA": 0.15, ...},
    "COLONOSCOPY": {...}
  },
  "specificity": {"FIT": 0.95, ...},
  "compliance": {...},
  "costs": {...}
}
```

### 2. 筛查策略 (Excel)
- **strategies工作表**: 策略定义
- **tool_configs工作表**: 工具配置

### 3. 基准数据 (Excel)
- 多工作表支持
- 自动识别发病率、死亡率、腺瘤患病率

### 4. 寿命表 (Excel/CSV)
- 支持按性别分组的死亡率数据
- 灵活的列名识别

### 5. 人口构成 (Excel)
- 年龄分布数据
- 性别比例计算
- 自动标准化

## 错误处理

- **文件不存在**: 自动使用默认数据
- **格式错误**: 详细错误信息和建议
- **数据验证**: 自动检查和修正
- **向后兼容**: 不影响现有代码

## 性能优化

- **数据缓存**: 避免重复加载
- **延迟加载**: 按需加载数据
- **内存优化**: 最小化内存占用

## 总结

通过这次整合，实现了：

1. **统一入口**: 所有数据加载通过一个模块
2. **路径统一**: 所有配置文件在config目录
3. **功能完整**: 支持所有类型的数据加载
4. **易于使用**: 简化的API和自动化处理
5. **向后兼容**: 不破坏现有代码
6. **错误处理**: 完善的错误处理和默认值
7. **性能优化**: 缓存和延迟加载机制

这大大提高了模型的可维护性和用户友好性，使得数据管理更加集中和高效。
