#!/usr/bin/env python3
"""
简单的筛查配置加载示例
演示如何使用Excel和JSON格式的配置文件来初始化模型
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.ccsm.core.model import ColorectalCancerMicrosimulationModel, ModelConfiguration


def main():
    """主函数"""
    print("=" * 60)
    print("筛查配置加载示例")
    print("=" * 60)
    
    # 方法1: 在模型初始化时从配置文件加载
    print("\n方法1: 模型初始化时从配置文件加载")
    print("-" * 40)
    
    config = ModelConfiguration(
        initial_population=1000,
        load_config_from_files=True,
        config_dir="config",
        screening_params_file="screening_parameters.json",
        screening_strategies_file="screening_strategies.xlsx"
    )
    
    model1 = ColorectalCancerMicrosimulationModel(config=config)
    
    print(f"✅ 模型1加载完成，可用策略数量: {len(model1.list_available_strategies())}")
    print("可用策略:")
    for strategy in model1.list_available_strategies():
        print(f"  - {strategy}")
    
    # 方法2: 运行时加载配置
    print("\n方法2: 运行时加载配置")
    print("-" * 40)
    
    model2 = ColorectalCancerMicrosimulationModel(initial_population=1000)
    print(f"初始策略数量: {len(model2.list_available_strategies())}")
    
    # 加载配置
    model2.load_screening_config_from_files(
        params_file="config/screening_parameters.json",
        strategies_file="config/screening_strategies.xlsx"
    )
    
    print(f"加载后策略数量: {len(model2.list_available_strategies())}")
    
    # 方法3: 创建和保存自定义配置
    print("\n方法3: 创建和保存自定义配置")
    print("-" * 40)
    
    # 创建配置模板
    model3 = ColorectalCancerMicrosimulationModel(initial_population=1000)
    model3.create_screening_config_templates()
    
    # 保存当前配置
    model3.save_screening_config_to_files(
        params_file="example_screening_parameters.json",
        strategies_file="example_screening_strategies.xlsx"
    )
    
    print("\n✅ 示例完成！")
    print("\n配置文件说明:")
    print("1. screening_parameters.json - 筛查参数配置（敏感性、特异性、依从性、成本等）")
    print("2. screening_strategies.xlsx - 筛查策略配置（包含strategies和tool_configs两个工作表）")
    print("\n您可以编辑这些文件来自定义筛查参数和策略。")


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n❌ 运行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
