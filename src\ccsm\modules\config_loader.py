"""
筛查配置加载器模块
支持从Excel和JSON文件加载筛查工具和策略配置
"""

import json
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional, Union, Any
from dataclasses import asdict

from .screening import (
    ScreeningParameters, ScreeningStrategy, ScreeningToolConfig,
    ScreeningTool
)
from ..core.enums import CancerStage


class ScreeningConfigLoader:
    """筛查配置加载器"""
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化配置加载器
        
        Args:
            config_dir: 配置文件目录，默认为项目根目录下的config文件夹
        """
        if config_dir is None:
            # 默认使用项目根目录下的config文件夹
            self.config_dir = Path(__file__).parent.parent.parent.parent / "config"
        else:
            self.config_dir = Path(config_dir)
        
        # 确保配置目录存在
        self.config_dir.mkdir(exist_ok=True)
        
        # 默认配置文件路径
        self.screening_params_file = self.config_dir / "screening_parameters.json"
        self.screening_strategies_file = self.config_dir / "screening_strategies.xlsx"
        self.screening_tools_file = self.config_dir / "screening_tools.xlsx"
    
    def load_screening_parameters_from_json(self, file_path: Optional[str] = None) -> ScreeningParameters:
        """
        从JSON文件加载筛查参数
        
        Args:
            file_path: JSON文件路径，如果为None则使用默认路径
            
        Returns:
            筛查参数对象
        """
        if file_path is None:
            file_path = self.screening_params_file
        else:
            file_path = Path(file_path)
        
        if not file_path.exists():
            print(f"❌ 筛查参数文件不存在: {file_path}")
            print("使用默认参数")
            return ScreeningParameters()
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 转换字符串键为枚举
            params = self._convert_json_to_screening_parameters(data)
            print(f"✅ 成功从JSON加载筛查参数: {file_path}")
            return params
            
        except Exception as e:
            print(f"❌ 加载筛查参数失败: {e}")
            print("使用默认参数")
            return ScreeningParameters()
    
    def load_screening_strategies_from_excel(self, file_path: Optional[str] = None) -> List[ScreeningStrategy]:
        """
        从Excel文件加载筛查策略
        
        Args:
            file_path: Excel文件路径，如果为None则使用默认路径
            
        Returns:
            筛查策略列表
        """
        if file_path is None:
            file_path = self.screening_strategies_file
        else:
            file_path = Path(file_path)
        
        if not file_path.exists():
            print(f"❌ 筛查策略文件不存在: {file_path}")
            return []
        
        try:
            # 读取策略配置表
            strategies_df = pd.read_excel(file_path, sheet_name='strategies')
            # 读取工具配置表
            tools_df = pd.read_excel(file_path, sheet_name='tool_configs')
            
            strategies = self._convert_excel_to_strategies(strategies_df, tools_df)
            print(f"✅ 成功从Excel加载 {len(strategies)} 个筛查策略: {file_path}")
            return strategies
            
        except Exception as e:
            print(f"❌ 加载筛查策略失败: {e}")
            return []
    
    def load_screening_tools_from_excel(self, file_path: Optional[str] = None) -> Dict[str, List[ScreeningToolConfig]]:
        """
        从Excel文件加载筛查工具配置
        
        Args:
            file_path: Excel文件路径，如果为None则使用默认路径
            
        Returns:
            按策略名称分组的筛查工具配置字典
        """
        if file_path is None:
            file_path = self.screening_tools_file
        else:
            file_path = Path(file_path)
        
        if not file_path.exists():
            print(f"❌ 筛查工具配置文件不存在: {file_path}")
            return {}
        
        try:
            df = pd.read_excel(file_path)
            tool_configs = self._convert_excel_to_tool_configs(df)
            print(f"✅ 成功从Excel加载筛查工具配置: {file_path}")
            return tool_configs
            
        except Exception as e:
            print(f"❌ 加载筛查工具配置失败: {e}")
            return {}
    
    def save_screening_parameters_to_json(self, params: ScreeningParameters, 
                                        file_path: Optional[str] = None) -> None:
        """
        将筛查参数保存到JSON文件
        
        Args:
            params: 筛查参数对象
            file_path: JSON文件路径，如果为None则使用默认路径
        """
        if file_path is None:
            file_path = self.screening_params_file
        else:
            file_path = Path(file_path)
        
        try:
            # 转换为可序列化的字典
            data = self._convert_screening_parameters_to_json(params)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 筛查参数已保存到JSON: {file_path}")
            
        except Exception as e:
            print(f"❌ 保存筛查参数失败: {e}")
    
    def save_screening_strategies_to_excel(self, strategies: List[ScreeningStrategy],
                                         file_path: Optional[str] = None) -> None:
        """
        将筛查策略保存到Excel文件
        
        Args:
            strategies: 筛查策略列表
            file_path: Excel文件路径，如果为None则使用默认路径
        """
        if file_path is None:
            file_path = self.screening_strategies_file
        else:
            file_path = Path(file_path)
        
        try:
            # 转换策略为DataFrame
            strategies_data, tools_data = self._convert_strategies_to_excel(strategies)
            
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                strategies_data.to_excel(writer, sheet_name='strategies', index=False)
                tools_data.to_excel(writer, sheet_name='tool_configs', index=False)
            
            print(f"✅ 筛查策略已保存到Excel: {file_path}")
            
        except Exception as e:
            print(f"❌ 保存筛查策略失败: {e}")
    
    def create_template_files(self) -> None:
        """创建模板配置文件"""
        # 创建默认筛查参数模板
        default_params = ScreeningParameters()
        self.save_screening_parameters_to_json(default_params)
        
        # 创建筛查策略模板
        template_strategies = self._create_template_strategies()
        self.save_screening_strategies_to_excel(template_strategies)
        
        print(f"✅ 模板配置文件已创建在: {self.config_dir}")
    
    def _convert_json_to_screening_parameters(self, data: Dict) -> ScreeningParameters:
        """将JSON数据转换为筛查参数对象"""
        # 转换敏感性数据
        sensitivity = {}
        if 'sensitivity' in data:
            for tool_str, stages_dict in data['sensitivity'].items():
                tool = ScreeningTool(tool_str)
                sensitivity[tool] = {}
                for stage_str, value in stages_dict.items():
                    # 根据字符串名称获取对应的CancerStage枚举
                    stage = self._get_cancer_stage_by_name(stage_str)
                    if stage:
                        sensitivity[tool][stage] = float(value)
        
        # 转换特异性数据
        specificity = {}
        if 'specificity' in data:
            for tool_str, value in data['specificity'].items():
                tool = ScreeningTool(tool_str)
                specificity[tool] = float(value)
        
        # 转换依从性数据
        compliance = {}
        if 'compliance' in data:
            for tool_str, value in data['compliance'].items():
                tool = ScreeningTool(tool_str)
                compliance[tool] = float(value)
        
        # 转换随访依从性数据
        follow_up_compliance = {}
        if 'follow_up_compliance' in data:
            for tool_str, value in data['follow_up_compliance'].items():
                tool = ScreeningTool(tool_str)
                follow_up_compliance[tool] = float(value)
        
        # 转换成本数据
        costs = {}
        if 'costs' in data:
            for tool_str, value in data['costs'].items():
                tool = ScreeningTool(tool_str)
                costs[tool] = float(value)
        
        return ScreeningParameters(
            sensitivity=sensitivity or None,
            specificity=specificity or None,
            compliance=compliance or None,
            follow_up_compliance=follow_up_compliance or None,
            costs=costs or None
        )
    
    def _convert_screening_parameters_to_json(self, params: ScreeningParameters) -> Dict:
        """将筛查参数对象转换为JSON可序列化的字典"""
        data = {}
        
        # 转换敏感性数据
        if params.sensitivity:
            data['sensitivity'] = {}
            for tool, stages_dict in params.sensitivity.items():
                data['sensitivity'][tool.value] = {}
                for stage, value in stages_dict.items():
                    data['sensitivity'][tool.value][stage.value] = value
        
        # 转换特异性数据
        if params.specificity:
            data['specificity'] = {tool.value: value for tool, value in params.specificity.items()}
        
        # 转换依从性数据
        if params.compliance:
            data['compliance'] = {tool.value: value for tool, value in params.compliance.items()}
        
        # 转换随访依从性数据
        if params.follow_up_compliance:
            data['follow_up_compliance'] = {tool.value: value for tool, value in params.follow_up_compliance.items()}
        
        # 转换成本数据
        if params.costs:
            data['costs'] = {tool.value: value for tool, value in params.costs.items()}
        
        return data

    def _get_cancer_stage_by_name(self, stage_name: str) -> Optional[CancerStage]:
        """根据名称获取CancerStage枚举"""
        stage_mapping = {
            'NORMAL': CancerStage.NORMAL,
            'LOW_RISK_ADENOMA': CancerStage.LOW_RISK_ADENOMA,
            'HIGH_RISK_ADENOMA': CancerStage.HIGH_RISK_ADENOMA,
            'SMALL_SERRATED_ADENOMA': CancerStage.SMALL_SERRATED_ADENOMA,
            'LARGE_SERRATED_ADENOMA': CancerStage.LARGE_SERRATED_ADENOMA,
            'PRECLINICAL_CANCER': CancerStage.PRECLINICAL_CANCER,
            'CLINICAL_CANCER_STAGE_I': CancerStage.CLINICAL_CANCER_STAGE_I,
            'CLINICAL_CANCER_STAGE_II': CancerStage.CLINICAL_CANCER_STAGE_II,
            'CLINICAL_CANCER_STAGE_III': CancerStage.CLINICAL_CANCER_STAGE_III,
            'CLINICAL_CANCER_STAGE_IV': CancerStage.CLINICAL_CANCER_STAGE_IV
        }
        return stage_mapping.get(stage_name.upper())

    def _get_screening_tool_by_name(self, tool_name: str) -> Optional[ScreeningTool]:
        """根据名称获取ScreeningTool枚举"""
        tool_mapping = {
            'FIT': ScreeningTool.FIT,
            'COLONOSCOPY': ScreeningTool.COLONOSCOPY,
            'SIGMOIDOSCOPY': ScreeningTool.SIGMOIDOSCOPY,
            'RISK_QUESTIONNAIRE': ScreeningTool.RISK_QUESTIONNAIRE,
            'RISKQUESTIONNAIRE': ScreeningTool.RISK_QUESTIONNAIRE,  # 兼容性
            'OTHER': ScreeningTool.OTHER
        }
        return tool_mapping.get(tool_name.upper())

    def _convert_excel_to_strategies(self, strategies_df: pd.DataFrame,
                                   tools_df: pd.DataFrame) -> List[ScreeningStrategy]:
        """将Excel数据转换为筛查策略列表"""
        strategies = []

        for _, row in strategies_df.iterrows():
            strategy_name = row['strategy_name']

            # 获取该策略的工具配置
            strategy_tools = tools_df[tools_df['strategy_name'] == strategy_name]
            tool_configs = []

            for _, tool_row in strategy_tools.iterrows():
                # 使用辅助方法获取工具枚举
                tool = self._get_screening_tool_by_name(str(tool_row['tool']))
                if not tool:
                    print(f"⚠️ 未知的筛查工具: {tool_row['tool']}")
                    continue

                tool_config = ScreeningToolConfig(
                    tool=tool,
                    start_age=int(tool_row['start_age']),
                    end_age=int(tool_row['end_age']),
                    interval=float(tool_row['interval']),
                    compliance_rate=float(tool_row['compliance_rate']) if pd.notna(tool_row['compliance_rate']) else None,
                    follow_up_compliance_rate=float(tool_row['follow_up_compliance_rate']) if pd.notna(tool_row['follow_up_compliance_rate']) else None
                )
                tool_configs.append(tool_config)

            strategy = ScreeningStrategy(
                name=strategy_name,
                tool_configs=tool_configs,
                sequential=bool(row['sequential']) if pd.notna(row['sequential']) else True,
                risk_stratified=bool(row['risk_stratified']) if pd.notna(row['risk_stratified']) else False,
                high_risk_interval=float(row['high_risk_interval']) if pd.notna(row['high_risk_interval']) else None
            )
            strategies.append(strategy)

        return strategies

    def _convert_excel_to_tool_configs(self, df: pd.DataFrame) -> Dict[str, List[ScreeningToolConfig]]:
        """将Excel数据转换为工具配置字典"""
        tool_configs = {}

        for _, row in df.iterrows():
            strategy_name = row['strategy_name']

            if strategy_name not in tool_configs:
                tool_configs[strategy_name] = []

            # 使用辅助方法获取工具枚举
            tool = self._get_screening_tool_by_name(str(row['tool']))
            if not tool:
                print(f"⚠️ 未知的筛查工具: {row['tool']}")
                continue

            tool_config = ScreeningToolConfig(
                tool=tool,
                start_age=int(row['start_age']),
                end_age=int(row['end_age']),
                interval=float(row['interval']),
                compliance_rate=float(row['compliance_rate']) if pd.notna(row['compliance_rate']) else None,
                follow_up_compliance_rate=float(row['follow_up_compliance_rate']) if pd.notna(row['follow_up_compliance_rate']) else None
            )
            tool_configs[strategy_name].append(tool_config)

        return tool_configs

    def _convert_strategies_to_excel(self, strategies: List[ScreeningStrategy]) -> tuple[pd.DataFrame, pd.DataFrame]:
        """将筛查策略转换为Excel格式的DataFrame"""
        strategies_data = []
        tools_data = []

        for strategy in strategies:
            # 策略基本信息
            strategy_row = {
                'strategy_name': strategy.name,
                'sequential': strategy.sequential,
                'risk_stratified': strategy.risk_stratified,
                'high_risk_interval': strategy.high_risk_interval
            }
            strategies_data.append(strategy_row)

            # 工具配置信息
            if strategy.tool_configs:
                for tool_config in strategy.tool_configs:
                    tool_row = {
                        'strategy_name': strategy.name,
                        'tool': tool_config.tool.value,
                        'start_age': tool_config.start_age,
                        'end_age': tool_config.end_age,
                        'interval': tool_config.interval,
                        'compliance_rate': tool_config.compliance_rate,
                        'follow_up_compliance_rate': tool_config.follow_up_compliance_rate
                    }
                    tools_data.append(tool_row)

        return pd.DataFrame(strategies_data), pd.DataFrame(tools_data)

    def _create_template_strategies(self) -> List[ScreeningStrategy]:
        """创建模板筛查策略"""
        strategies = []

        # 年度FIT策略
        annual_fit = ScreeningStrategy(
            name="annual_fit_template",
            tool_configs=[
                ScreeningToolConfig(
                    tool=ScreeningTool.FIT,
                    start_age=50,
                    end_age=75,
                    interval=1.0,
                    compliance_rate=0.70,
                    follow_up_compliance_rate=0.75
                )
            ],
            sequential=False,
            risk_stratified=False
        )
        strategies.append(annual_fit)

        # 风险分层策略
        risk_stratified = ScreeningStrategy(
            name="risk_stratified_template",
            tool_configs=[
                ScreeningToolConfig(
                    tool=ScreeningTool.RISK_QUESTIONNAIRE,
                    start_age=45,
                    end_age=50,
                    interval=5.0,
                    compliance_rate=0.80
                ),
                ScreeningToolConfig(
                    tool=ScreeningTool.FIT,
                    start_age=50,
                    end_age=65,
                    interval=1.0,
                    compliance_rate=0.70,
                    follow_up_compliance_rate=0.75
                ),
                ScreeningToolConfig(
                    tool=ScreeningTool.COLONOSCOPY,
                    start_age=65,
                    end_age=75,
                    interval=10.0,
                    compliance_rate=0.60
                )
            ],
            sequential=True,
            risk_stratified=True,
            high_risk_interval=1.0
        )
        strategies.append(risk_stratified)

        return strategies


class ScreeningConfigManager:
    """筛查配置管理器"""

    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化配置管理器

        Args:
            config_dir: 配置文件目录
        """
        self.loader = ScreeningConfigLoader(config_dir)
        self.current_parameters: Optional[ScreeningParameters] = None
        self.current_strategies: List[ScreeningStrategy] = []

    def initialize_from_config(self,
                             params_file: Optional[str] = None,
                             strategies_file: Optional[str] = None) -> tuple[ScreeningParameters, List[ScreeningStrategy]]:
        """
        从配置文件初始化筛查参数和策略

        Args:
            params_file: 参数文件路径
            strategies_file: 策略文件路径

        Returns:
            (筛查参数, 筛查策略列表)
        """
        # 加载筛查参数
        self.current_parameters = self.loader.load_screening_parameters_from_json(params_file)

        # 加载筛查策略
        self.current_strategies = self.loader.load_screening_strategies_from_excel(strategies_file)

        return self.current_parameters, self.current_strategies

    def get_strategy_by_name(self, name: str) -> Optional[ScreeningStrategy]:
        """根据名称获取筛查策略"""
        for strategy in self.current_strategies:
            if strategy.name == name:
                return strategy
        return None

    def add_strategy(self, strategy: ScreeningStrategy) -> None:
        """添加新的筛查策略"""
        # 检查是否已存在同名策略
        existing_strategy = self.get_strategy_by_name(strategy.name)
        if existing_strategy:
            # 替换现有策略
            index = self.current_strategies.index(existing_strategy)
            self.current_strategies[index] = strategy
            print(f"已更新筛查策略: {strategy.name}")
        else:
            # 添加新策略
            self.current_strategies.append(strategy)
            print(f"已添加筛查策略: {strategy.name}")

    def remove_strategy(self, name: str) -> bool:
        """移除筛查策略"""
        strategy = self.get_strategy_by_name(name)
        if strategy:
            self.current_strategies.remove(strategy)
            print(f"已移除筛查策略: {name}")
            return True
        else:
            print(f"未找到筛查策略: {name}")
            return False

    def list_strategies(self) -> List[str]:
        """列出所有策略名称"""
        return [strategy.name for strategy in self.current_strategies]

    def save_current_config(self,
                          params_file: Optional[str] = None,
                          strategies_file: Optional[str] = None) -> None:
        """保存当前配置"""
        if self.current_parameters:
            self.loader.save_screening_parameters_to_json(self.current_parameters, params_file)

        if self.current_strategies:
            self.loader.save_screening_strategies_to_excel(self.current_strategies, strategies_file)

    def create_config_templates(self) -> None:
        """创建配置模板文件"""
        self.loader.create_template_files()
        print("配置模板文件已创建，您可以根据需要修改这些文件")
